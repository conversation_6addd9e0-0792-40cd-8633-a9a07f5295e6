<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';
import type { Recordable } from '@vben/types';

import { computed, ref } from 'vue';

import { AuthenticationForgetPassword, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { getEmailByUserNameApi, getMobileByUserNameApi, sendEmailCodeApi } from '#/api';

defineOptions({ name: 'ForgetPassword' });

const loading = ref(false);
const active = ref<1 | 2 | 3>(1);
interface StepConfig {
  [key: number]: VbenFormSchema[]; // 明确表示可以用数字索引
}
const confirmAccount = ref('');
const findType = ref('');
const formSchema = computed((): VbenFormSchema[] => {
  const step: StepConfig = {
    1: [
      {
        component: 'VbenInput',
        componentProps: {
          placeholder: '请输入找回账号',
        },
        fieldName: 'userName',
        label: $t('authentication.username'),
        rules: z.string().min(1, { message: $t('authentication.userNameTip') }),
      },
      {
        component: 'VbenSelect',
        componentProps: {
          placeholder: '请选择找回方式',
          options: [
            { label: '通过邮箱找回', value: 'email' },
            { label: '通过手机找回', value: 'mobile' },
          ],
        },
        fieldName: 'type',
        label: '找回方式',
        rules: z.string().min(1, { message: '请选择找回方式' }),
      },
    ],
    2: [
      {
        component: 'VbenInput',
        componentProps: {
          disabled: true,
          modelValue: confirmAccount.value,
        },
        fieldName: 'showAccount',
        label: '确认账号',
      },
      {
        component: 'VbenInput',
        componentProps: {
          placeholder: '请确认邮箱/手机号',
        },
        fieldName: 'confirmAccount',
        label: '确认账号',
        rules: z.string().min(1, { message: '请输入请确认邮箱/手机号' }),
      },
    ],
    3: [],
  };
  return step[active.value]!;
});
const AuthForgetPasswordRef = ref()
async function handleSubmit(value: Recordable<any>) {
  // eslint-disable-next-line no-console
  console.log('value:', value);
  findType.value = value.type;
  if (active.value === 1) {
    let api = getMobileByUserNameApi;
    if (value.type === 'email') {
      // 通过邮箱找回
      api = getEmailByUserNameApi;
    }
    confirmAccount.value = await api({ userName: value.userName });
    active.value = 2;
  } else if (active.value === 2) {
    let api = sendEmailCodeApi;
    const params = { email: value.confirmAccount };
    if (findType.value === 'mobile') {
      // 通过手机找回
      api = sendSmsCodeApi;
    }
    await api({ userName: value.confirmUserName, email: value.confirmAccount });
    active.value = 3;
  }
}
</script>

<template>
  <AuthenticationForgetPassword ref="AuthForgetPasswordRef" :form-schema="formSchema" :loading="loading" @submit="handleSubmit" />
</template>
